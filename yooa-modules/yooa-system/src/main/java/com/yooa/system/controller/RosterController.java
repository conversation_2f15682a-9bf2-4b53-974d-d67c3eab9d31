package com.yooa.system.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.common.core.domain.R;
import com.yooa.common.core.text.Convert;
import com.yooa.common.core.utils.poi.ExcelUtil;
import com.yooa.common.core.web.controller.BaseController;
import com.yooa.common.core.web.domain.AjaxResult;
import com.yooa.common.log.annotation.Log;
import com.yooa.common.log.enums.BusinessType;
import com.yooa.common.security.utils.SecurityUtils;
import com.yooa.external.api.RemoteDingTalkUserService;
import com.yooa.external.api.response.DingTalkUserLeaveListRes;
import com.yooa.system.api.domain.SysRoster;
import com.yooa.system.api.domain.SysRosterUser;
import com.yooa.system.api.domain.SysUser;
import com.yooa.system.api.domain.SysUserPd;
import com.yooa.system.api.domain.dto.RosterEditDto;
import com.yooa.system.api.domain.dto.RosterSaveDto;
import com.yooa.system.api.domain.dto.UserEditDto;
import com.yooa.system.api.domain.dto.UserSaveDto;
import com.yooa.system.api.domain.query.RosterQuery;
import com.yooa.system.api.domain.vo.RosterSensitiveVo;
import com.yooa.system.service.*;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 花名册/员工 - 控制层
 */
@Validated
@AllArgsConstructor
@RestController
@RequestMapping("/roster")
public class RosterController extends BaseController {

    private final RosterService rosterService;
    private final RosterUserService rosterUserService;
    private final UserService userService;
    private final ConfigService configService;
    private final UserPdService userPdService;
    private final PersonnelChangeRecordService personnelChangeRecordService;
    private final RemoteDingTalkUserService remoteDingTalkUserService;

    /**
     * 获取花名册列表
     */
    @GetMapping("/list")
    public AjaxResult list(Page<RosterSensitiveVo> page, RosterQuery query) {
        return success(page.setRecords(rosterService.getRosterList(page, query)));
    }

    @GetMapping("/allList")
    public AjaxResult allList(Page<RosterSensitiveVo> page, RosterQuery query) {
        return success(page.setRecords(rosterService.getAllRosterList(page, query)));
    }

    /**
     * 获取花名册详情
     */
    @GetMapping("/{rosterId}")
    public AjaxResult info(@PathVariable Long rosterId) {
        return success(rosterService.getRosterById(rosterId));
    }

    /**
     * 导出花名册数据
     */
    // @RequiresPermissions("system:roster:import")
    @PostMapping("/import-template")
    public void importTemplate(HttpServletResponse response, RosterQuery query) {
        ExcelUtil<RosterSensitiveVo> util = new ExcelUtil<RosterSensitiveVo>(RosterSensitiveVo.class);
        util.importTemplateExcel(response, "花名册导入模板");
    }

    /**
     * 导出花名册数据
     */
    @Log(title = "花名册", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RosterQuery query) {
        List<RosterSensitiveVo> list = rosterService.getRosterList(null, query);
        ExcelUtil<RosterSensitiveVo> util = new ExcelUtil<RosterSensitiveVo>(RosterSensitiveVo.class);
        util.exportExcel(response, list, "花名册导出数据");
    }

    /**
     * 新增花名册
     */
    @Log(title = "花名册", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult addSysRoster(@Valid @RequestBody RosterSaveDto rosterSaveDto) {
        return success(rosterService.save(rosterSaveDto));
    }

    /**
     * 修改花名册
     */
    @Log(title = "花名册", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult updSysRoster(@Valid @RequestBody RosterEditDto rosterEditDto) {
        // 身份证是不允许修改的，设置为空
        if (!SecurityUtils.isAdmin()) {
            rosterEditDto.setIdCardNumber(null);
            rosterEditDto.setBankCardNumber(null);
            rosterEditDto.setAlipayAccount(null);
        }

        // 同步修改员工所绑定的用户部门和用户角色信息
        List<SysRosterUser> rosterUsers = rosterUserService.listByRosterId(rosterEditDto.getRosterId());
        if (CollUtil.isNotEmpty(rosterUsers)) {
            rosterUsers.forEach(ru -> {
                SysUser user = userService.getById(ru.getUserId());
                String flowerName = StrUtil.isNotBlank(rosterEditDto.getFlowerName()) ? rosterEditDto.getFlowerName() : rosterEditDto.getRealName();
                user.setNickName(rosterEditDto.getRealName() + "-" + flowerName);
                String employeeStatus = rosterEditDto.getEmployeeStatus();
                if (StrUtil.isNotBlank(employeeStatus)) {
                    user.setStatus("0".equals(employeeStatus) || "1".equals(employeeStatus) ? "0" : "1");
                }
                // 只有主账号需要修改部门和角色
                if (ru.getUserId().equals(rosterEditDto.getOaUserId())) {
                    user.setDeptId(rosterEditDto.getDeptId());
                    user.setUserRole(Convert.toInt(rosterEditDto.getEmployeeRole()));
                }
                userService.updateById(user);
            });
        }

        return success(rosterService.updateById(rosterEditDto));
    }

    /**
     * 删除花名册
     */
    @Log(title = "花名册", businessType = BusinessType.DELETE)
    @DeleteMapping
    public AjaxResult delSysRoster(List<Long> rosterIds) {
        return success(rosterService.removeBatchByIds(rosterIds));
    }


    @GetMapping("/pd-account/{rosterId}")
    public AjaxResult getPDAccount(@PathVariable Long rosterId) {
        SysRoster roster = rosterService.getById(rosterId);
        if (ObjUtil.isEmpty(roster)) {
            return warn("员工信息异常");
        }

        Long userId = roster.getOaUserId();
        if (ObjUtil.isEmpty(userId)) {
            return warn("请先绑定OA账号");
        }

        return success(userPdService.mapByRosterId(rosterId));
    }

    @Valid
    @Log(title = "花名册", businessType = BusinessType.OTHER)
    @PostMapping("/pd-account/{rosterId}/{pdId}")
    public AjaxResult bindPDAccount(@PathVariable Long rosterId, @PathVariable Long pdId, @NotBlank(message = "绑定类型不能为空") String type) {
        SysRoster roster = rosterService.getById(rosterId);
        if (ObjUtil.isEmpty(roster)) {
            return warn("员工信息异常");
        }

        Long userId = roster.getOaUserId();
        if (ObjUtil.isEmpty(userId)) {
            return warn("请先绑定OA账号");
        }

        SysUserPd up = userPdService.getByPyId(pdId);
        if (ObjUtil.isNotEmpty(up)) {
            return warn("[" + pdId + "] 已被他人绑定，请勿重复绑定");
        }

        return success(userPdService.save(userId, pdId, type));
    }

    @Valid
    @Log(title = "花名册", businessType = BusinessType.OTHER)
    @DeleteMapping("/pd-account/{rosterId}/{pdId}")
    public AjaxResult removePdId(@PathVariable Long rosterId, @PathVariable Long pdId, @NotBlank(message = "绑定类型不能为空") String type) {
        SysRoster roster = rosterService.getById(rosterId);
        if (ObjUtil.isEmpty(roster)) {
            return warn("员工信息异常");
        }

        Long userId = roster.getOaUserId();
        if (ObjUtil.isEmpty(userId)) {
            return warn("请先绑定OA账号");
        }
        return success(userPdService.remove(userId, pdId));
    }

    @GetMapping("/oa-account/{rosterId}")
    public AjaxResult UserInfo(@PathVariable Long rosterId) {
        SysRoster roster = rosterService.getById(rosterId);
        if (ObjUtil.isEmpty(roster)) {
            return warn("员工信息异常");
        }
        return success(userService.selectUserById(roster.getOaUserId()));
    }

    @Valid
    @PutMapping("/oa-account/{rosterId}")
    public AjaxResult editUser(@PathVariable Long rosterId,
            @NotBlank(message = "用户账号不能为空") String userName,
            @NotNull(message = "角色不能为空") Long[] roleIds,
            @NotNull(message = "部门不能为空") Long deptId,
            String passWord
    ) {
        SysRoster roster = rosterService.getById(rosterId);
        if (ObjUtil.isEmpty(roster.getOaUserId())) {
            return warn("[" + roster.getRealName() + "]未有OA账号，请先创建");
        }
        SysUser user = userService.getOne(Wrappers.<SysUser>lambdaQuery().eq(SysUser::getUserName, userName));
        if (ObjUtil.isNotNull(user) && StrUtil.equals(userName, user.getUserName()) && !user.getUserId().equals(roster.getOaUserId())) {
            return error(user.getUserName() + "登录账号已存在");
        }
        UserEditDto userEditDto = new UserEditDto();
        userEditDto.setUserId(roster.getOaUserId());
        userEditDto.setUserName(userName);
        userEditDto.setUserRole(Convert.toInt(roster.getEmployeeRole()));
        userEditDto.setDeptId(deptId);
        userEditDto.setRoleIds(roleIds);
        if (StrUtil.isNotBlank(passWord)) {
            userEditDto.setPassword(SecurityUtils.encryptPassword(passWord));
        }
        return success(userService.updateUser(userEditDto));
    }


    @Valid
    @PostMapping("/oa-account/{rosterId}")
    public AjaxResult createUser(@PathVariable Long rosterId, @NotNull(message = "部门ID不能为空") Long deptId, @NotNull(message = "角色ID不能为空") Long[] roleIds) {
        SysRoster roster = rosterService.getById(rosterId);

        if (ObjUtil.isNotEmpty(roster.getOaUserId())) {
            return warn("[" + roster.getRealName() + "]已存在OA账号，请勿重复创建");
        }

        String initPassWord = configService.getConfigValueByKey("sys.user.initPassword");
        String idCardNumber = roster.getIdCardNumber();
        String randomNumber = StrUtil.isNotBlank(idCardNumber) && idCardNumber.length() == 18 ?
                StrUtil.sub(idCardNumber, 14, 18) : RandomUtil.randomNumbers(4);
        UserSaveDto userSaveDto = new UserSaveDto();
        userSaveDto.setUserName(roster.getRealName() + randomNumber);
        String strName = StrUtil.isNotBlank(roster.getFlowerName()) ? roster.getFlowerName() : roster.getRealName();
        userSaveDto.setNickName(roster.getRealName() + "-" + strName);
        userSaveDto.setUserRole(Convert.toInt(roster.getEmployeeRole()));
        userSaveDto.setDeptId(deptId);
        userSaveDto.setPassword(SecurityUtils.encryptPassword(initPassWord));
        userSaveDto.setRoleIds(roleIds);
        userService.insertUser(userSaveDto);

        roster.setDeptId(deptId);
        roster.setOaUserId(userSaveDto.getUserId());
        rosterService.updateById(roster);

        return success(rosterUserService.save(rosterId, userSaveDto.getUserId()));
    }

    @Valid
    @PostMapping("/oa-sub-account/{rosterId}")
    public AjaxResult createSubUser(@PathVariable Long rosterId, @NotNull(message = "部门ID不能为空") Long deptId, @NotNull(message = "角色ID不能为空") Long[] roleIds) {
        // TODO 暂时收回创建子账号权限
        if (!SecurityUtils.isAdmin()) {
            return warn("创建子账号权限已暂时收回，请联系管理员");
        }
        SysRoster roster = rosterService.getById(rosterId);

        List<SysRosterUser> rosterUserList = rosterUserService.listByRosterId(rosterId);
        if (CollUtil.isEmpty(rosterUserList)) {
            return warn("请先创建OA主账号");
        }

        String initPassWord = configService.getConfigValueByKey("sys.user.initPassword");
        String idCardNumber = roster.getIdCardNumber();
        String randomNumber = StrUtil.isNotBlank(idCardNumber) && idCardNumber.length() == 18 ?
                StrUtil.sub(idCardNumber, 14, 18) : RandomUtil.randomNumbers(4);
        UserSaveDto userSaveDto = new UserSaveDto();
        userSaveDto.setUserName(roster.getRealName() + randomNumber + "-sub" + (rosterUserList.size() + 1));
        String flowerName = StrUtil.isNotBlank(roster.getFlowerName()) ? roster.getFlowerName() : roster.getRealName();
        userSaveDto.setNickName(roster.getRealName() + "-" + flowerName);
        userSaveDto.setUserRole(Convert.toInt(roster.getEmployeeRole()));
        userSaveDto.setDeptId(deptId);
        userSaveDto.setPassword(SecurityUtils.encryptPassword(initPassWord));
        userSaveDto.setRoleIds(roleIds);
        userService.insertUser(userSaveDto);

        return success(rosterUserService.save(rosterId, userSaveDto.getUserId()));
    }


    @GetMapping("/allRoster")
    public AjaxResult allDeptAndRoster() {
        return success(rosterService.allDeptAndRoster());
    }


    // 测试接口
    @GetMapping("/test")
    public AjaxResult test() {
        R<DingTalkUserLeaveListRes> r = remoteDingTalkUserService.listByLeave(0L,50, SecurityConstants.INNER);
        DingTalkUserLeaveListRes data = r.getData();
        if (data.getHasMore() == true) {
            r = remoteDingTalkUserService.listByLeave(data.getNextToken(),50, SecurityConstants.INNER);
        }

        return null;
    }

}
