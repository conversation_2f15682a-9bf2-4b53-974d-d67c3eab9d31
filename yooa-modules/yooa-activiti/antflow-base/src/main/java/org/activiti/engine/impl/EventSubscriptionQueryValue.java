/* Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * 
 *      http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.activiti.engine.impl;

import java.io.Serializable;


/**
 * <AUTHOR>
 */
public class EventSubscriptionQueryValue implements Serializable {
  
  private static final long serialVersionUID = 1L;
  
  protected String eventType;
  protected String eventName;
  
  public EventSubscriptionQueryValue(String eventName, String eventType) {
    this.eventName = eventName;
    this.eventType = eventType;
  }

  public String getEventType() {
    return eventType;
  }
  
  public void setEventType(String eventType) {
    this.eventType = eventType;
  }
  
  public String getEventName() {
    return eventName;
  }
  
  public void setEventName(String eventName) {
    this.eventName = eventName;
  }
  
    

}
