<?xml version="1.0" encoding="UTF-8" ?>

<!--
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~       http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.activiti.engine.impl.persistence.entity.HistoricIdentityLinkEntity">
  
   <!-- INSERT HISTORIC IDENTITY LINK -->
  
  <insert id="insertHistoricIdentityLink" parameterType="org.activiti.engine.impl.persistence.entity.HistoricIdentityLinkEntity">
    insert into ${prefix}ACT_HI_IDENTITYLINK (ID_, TYPE_, USER_ID_, GROUP_ID_, TASK_ID_, PROC_INST_ID_)
    values (#{id, jdbcType=VARCHAR},
            #{type, jdbcType=VARCHAR},
            #{userId, jdbcType=VARCHAR},
            #{groupId, jdbcType=VARCHAR},
            #{taskId, jdbcType=VARCHAR},
            #{processInstanceId, jdbcType=VARCHAR})
  </insert>
  
  <insert id="bulkInsertHistoricIdentityLink" parameterType="java.util.List">
    insert into ${prefix}ACT_HI_IDENTITYLINK (ID_, TYPE_, USER_ID_, GROUP_ID_, TASK_ID_, PROC_INST_ID_)
    values 
      <foreach collection="list" item="historicIdentityLink" index="index" separator=",">
        (#{historicIdentityLink.id, jdbcType=VARCHAR},
         #{historicIdentityLink.type, jdbcType=VARCHAR},
         #{historicIdentityLink.userId, jdbcType=VARCHAR},
         #{historicIdentityLink.groupId, jdbcType=VARCHAR},
         #{historicIdentityLink.taskId, jdbcType=VARCHAR},
         #{historicIdentityLink.processInstanceId, jdbcType=VARCHAR})
      </foreach>
  </insert>
  
  <insert id="bulkInsertHistoricIdentityLink_oracle" parameterType="java.util.List">
    INSERT ALL 
      <foreach collection="list" item="historicIdentityLink" index="index">
        INTO ${prefix}ACT_HI_IDENTITYLINK (ID_, TYPE_, USER_ID_, GROUP_ID_, TASK_ID_, PROC_INST_ID_) VALUES 
          (#{historicIdentityLink.id, jdbcType=VARCHAR},
           #{historicIdentityLink.type, jdbcType=VARCHAR},
           #{historicIdentityLink.userId, jdbcType=VARCHAR},
           #{historicIdentityLink.groupId, jdbcType=VARCHAR},
           #{historicIdentityLink.taskId, jdbcType=VARCHAR},
           #{historicIdentityLink.processInstanceId, jdbcType=VARCHAR})
      </foreach>
    SELECT * FROM dual
  </insert>
  
  <!-- HISTORIC IDENTITY LINK DELETE -->
  
  <delete id="deleteHistoricIdentityLink" parameterType="string">
    delete from ${prefix}ACT_HI_IDENTITYLINK where ID_ = #{id}
  </delete>
  
  <delete id="bulkDeleteHistoricIdentityLink" parameterType="java.util.Collection">
    delete from ${prefix}ACT_HI_IDENTITYLINK where  
    <foreach item="identityLink" collection="list" index="index" separator=" or ">
        ID_ = #{identityLink.id, jdbcType=VARCHAR}
    </foreach>
  </delete>
  
  <!-- HISTORIC IDENTITY LINK RESULTMAP -->

  <resultMap id="historicIdentityLinkResultMap" type="org.activiti.engine.impl.persistence.entity.HistoricIdentityLinkEntity">
    <id property="id" column="ID_" jdbcType="VARCHAR" />
    <result property="type" column="TYPE_" jdbcType="VARCHAR" />
    <result property="userId" column="USER_ID_" jdbcType="VARCHAR" />
    <result property="groupId" column="GROUP_ID_" jdbcType="VARCHAR" />
    <result property="taskId" column="TASK_ID_" jdbcType="VARCHAR" />
    <result property="processInstanceId" column="PROC_INST_ID_" jdbcType="VARCHAR" />    
  </resultMap>

  <!-- HISTORIC IDENTITY LINK SELECT -->

  <select id="selectHistoricIdentityLink" parameterType="string" resultMap="historicIdentityLinkResultMap">
    select * from ${prefix}ACT_HI_IDENTITYLINK where ID_ = #{id}
  </select>
  
  <select id="selectHistoricIdentityLinksByTask" parameterType="org.activiti.engine.impl.db.ListQueryParameterObject" resultMap="historicIdentityLinkResultMap">
    select * from ${prefix}ACT_HI_IDENTITYLINK where TASK_ID_ = #{parameter}
  </select>
  
  <select id="selectHistoricIdentityLinksByProcessInstance" parameterType="org.activiti.engine.impl.db.ListQueryParameterObject" resultMap="historicIdentityLinkResultMap">
    select * from ${prefix}ACT_HI_IDENTITYLINK where PROC_INST_ID_ = #{parameter}
  </select>
  
 <select id="selectIdentityLinks" resultMap="historicIdentityLinkResultMap">
    select * from ${prefix}ACT_HI_IDENTITYLINK
  </select>
  
  <select id="selectHistoricIdentityLinkByTaskUserGroupAndType" parameterType="org.activiti.engine.impl.db.ListQueryParameterObject" resultMap="historicIdentityLinkResultMap">
    select * 
    from ${prefix}ACT_HI_IDENTITYLINK 
    where TASK_ID_ = #{parameter.taskId}
      <if test="parameter.userId != null">
        and USER_ID_ = #{parameter.userId}
      </if>
      <if test="parameter.groupId != null">
        and GROUP_ID_ = #{parameter.groupId}
      </if>
      <if test="parameter.type != null">
        and TYPE_ = #{parameter.type}
      </if>
  </select>
</mapper>
