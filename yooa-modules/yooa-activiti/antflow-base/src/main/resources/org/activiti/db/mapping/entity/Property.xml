<?xml version="1.0" encoding="UTF-8" ?> 

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd"> 
  
<mapper namespace="org.activiti.engine.impl.persistence.entity.PropertyEntity">

  <!-- PROPERTY INSERT -->
  
  <insert id="insertProperty" parameterType="org.activiti.engine.impl.persistence.entity.PropertyEntity">
      insert into ${prefix}ACT_GE_PROPERTY (
        NAME_,
        VALUE_,
        REV_
      ) values (
        #{name ,jdbcType=VARCHAR},
        #{value, jdbcType=VARCHAR},
        1
      )
  </insert>

  <insert id="bulkInsertProperty" parameterType="java.util.List">
      INSERT INTO ${prefix}ACT_GE_PROPERTY (NAME_, VALUE_, REV_) VALUES 
      <foreach collection="list" item="property" index="index" separator=","> 
        (#{property.name ,jdbcType=VARCHAR},
         #{property.value, jdbcType=VARCHAR},
         1)
      </foreach>
  </insert>

  <insert id="bulkInsertProperty_oracle" parameterType="java.util.List">
    INSERT ALL 
    <foreach collection="list" item="property" index="index">
      INTO ${prefix}ACT_GE_PROPERTY (NAME_, VALUE_, REV_) VALUES
      (#{property.name ,jdbcType=VARCHAR},
       #{property.value, jdbcType=VARCHAR},
       1)
    </foreach>
    SELECT * FROM dual
  </insert>

  <!-- PROPERTY UPDATE -->
  
  <update id="updateProperty" parameterType="org.activiti.engine.impl.persistence.entity.PropertyEntity">
    update ${prefix}ACT_GE_PROPERTY
    <set>
      REV_ = #{revisionNext, jdbcType=INTEGER},
      VALUE_ = #{value, jdbcType=VARCHAR}
    </set>
    where NAME_ = #{name, jdbcType=VARCHAR}
      and REV_ = #{revision, jdbcType=INTEGER}
  </update>

  <!-- PROPERTY DELETE -->
  
  <delete id="deleteProperty" parameterType="org.activiti.engine.impl.persistence.entity.PropertyEntity">
    delete from ${prefix}ACT_GE_PROPERTY where NAME_ = #{propertyName} and REV_ = #{revision}
  </delete>


  <!-- PROPERTY SELECT -->
  
  <resultMap id="propertyResultMap" type="org.activiti.engine.impl.persistence.entity.PropertyEntity">
    <id property="name" column="NAME_" jdbcType="VARCHAR" />
    <result property="value" column="VALUE_" jdbcType="VARCHAR" />
    <result property="revision" column="REV_" jdbcType="INTEGER" />
  </resultMap>
  
  <select id="selectDbSchemaVersion" resultType="string">
    select VALUE_ from ${prefix}ACT_GE_PROPERTY where NAME_ = 'schema.version'
  </select>

  <select id="selectProperty" parameterType="string" resultMap="propertyResultMap">
    select * from ${prefix}ACT_GE_PROPERTY where NAME_ = #{name}
  </select>
  
  <select id="selectProperties" resultMap="propertyResultMap">
    select * from ${prefix}ACT_GE_PROPERTY 
  </select>
  
</mapper>
